package ir.rahavardit.ariel.ui.edittransaction

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.SessionManager
import ir.rahavardit.ariel.data.model.BankItem
import ir.rahavardit.ariel.data.model.ExpenditureObject
import ir.rahavardit.ariel.data.model.IncomeObject
import ir.rahavardit.ariel.data.model.TagItem
import ir.rahavardit.ariel.data.model.TransactionCategoryItem
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.databinding.FragmentEditTransactionBinding
import ir.rahavardit.ariel.ui.components.JalaliDatePickerDialog
import ir.rahavardit.ariel.utils.JalaliDateUtils
import ir.rahavardit.ariel.utils.PersianUtils

/**
 * Fragment for editing a transaction.
 */
class EditTransactionFragment : Fragment() {

    private var _binding: FragmentEditTransactionBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EditTransactionViewModel
    private lateinit var sessionManager: SessionManager

    private var shortUuid: String? = null
    private var incomeObject: IncomeObject? = null
    private var expenditureObject: ExpenditureObject? = null

    private var selectedDate: String? = null
    private var selectedMode: String? = null
    private var selectedBankId: Int? = null
    private var selectedCategoryId: Int? = null
    private val selectedTagIds = mutableListOf<Int>()
    private val selectedTagTitles = mutableListOf<String>()

    private var availableTags: List<TagItem> = emptyList()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EditTransactionViewModel::class.java)
        _binding = FragmentEditTransactionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")
        incomeObject = arguments?.getParcelable("incomeObject")
        expenditureObject = arguments?.getParcelable("expenditureObject")

        // Check if we have the transaction data
        if (incomeObject == null && expenditureObject == null) {
            Toast.makeText(
                requireContext(),
                "Transaction data not found",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        populateForm()
        setupListeners()
        observeViewModel()
        fetchData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    /**
     * Populates the form with the transaction data.
     */
    private fun populateForm() {
        incomeObject?.let { income ->
            // Set title
            binding.etTransactionTitle.setText(income.title ?: "")

            // Set amount
            binding.etTransactionAmount.setText(income.amount.toString())

            // Set date
            selectedDate = JalaliDateUtils.formatJalaliDate(income.year, income.month, income.day)
            val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
            binding.etTransactionDate.setText(displayDate)

            // Set mode
            selectedMode = income.mode

            // Set bank
            selectedBankId = income.bankInfo?.id

            // Set category
            selectedCategoryId = income.categoryInfo?.id

            // Set tags
            selectedTagIds.clear()
            selectedTagTitles.clear()
            income.tags?.let { selectedTagIds.addAll(it) }
            income.tagsNames?.let { selectedTagTitles.addAll(it) }
            binding.etTransactionTags.setText(selectedTagTitles.joinToString(", "))
        }

        expenditureObject?.let { expenditure ->
            // Set title
            binding.etTransactionTitle.setText(expenditure.title ?: "")

            // Set amount
            binding.etTransactionAmount.setText(expenditure.amount.toString())

            // Set date
            selectedDate = JalaliDateUtils.formatJalaliDate(expenditure.year, expenditure.month, expenditure.day)
            val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
            binding.etTransactionDate.setText(displayDate)

            // Set mode
            selectedMode = expenditure.mode

            // Set bank
            selectedBankId = expenditure.bankInfo?.id

            // Set category
            selectedCategoryId = expenditure.categoryInfo?.id

            // Set tags
            selectedTagIds.clear()
            selectedTagTitles.clear()
            expenditure.tags?.let { selectedTagIds.addAll(it) }
            expenditure.tagsNames?.let { selectedTagTitles.addAll(it) }
            binding.etTransactionTags.setText(selectedTagTitles.joinToString(", "))
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Date picker
        binding.etTransactionDate.setOnClickListener {
            showDatePicker()
        }

        // Tags multi-select
        binding.etTransactionTags.setOnClickListener {
            showTagsDialog()
        }

        // Mode dropdown
        binding.dropdownTransactionMode.setOnItemClickListener { _, _, position, _ ->
            val modes = viewModel.modes.value
            if (modes != null && position < modes.size) {
                selectedMode = modes[position].value
                binding.tilTransactionMode.error = null
            }
        }

        // Bank dropdown
        binding.dropdownTransactionBank.setOnItemClickListener { _, _, position, _ ->
            val banks = viewModel.banks.value?.results
            if (banks != null) {
                if (position == 0) {
                    // Empty option selected
                    selectedBankId = null
                } else if (position - 1 < banks.size) {
                    selectedBankId = banks[position - 1].id
                }
                binding.tilTransactionBank.error = null
            }
        }

        // Category dropdown
        binding.dropdownTransactionCategory.setOnItemClickListener { _, _, position, _ ->
            val categories = viewModel.categories.value?.results
            if (categories != null && position < categories.size) {
                selectedCategoryId = categories[position].id
                binding.tilTransactionCategory.error = null
            }
        }

        // Submit button
        binding.btnSubmit.setOnClickListener {
            val title = binding.etTransactionTitle.text.toString().trim()
            val amount = binding.etTransactionAmount.text.toString().trim()

            val validationResult = viewModel.validateInputs(
                selectedDate,
                amount,
                selectedMode,
                selectedCategoryId?.toString()
            )

            when {
                !validationResult.isDateValid -> {
                    binding.tilTransactionDate.error = getString(R.string.please_select_date)
                }
                !validationResult.isAmountValid -> {
                    binding.tilTransactionAmount.error = getString(R.string.please_enter_amount)
                }
                !validationResult.isModeValid -> {
                    binding.tilTransactionMode.error = getString(R.string.please_select_mode)
                }
                !validationResult.isCategoryValid -> {
                    binding.tilTransactionCategory.error = getString(R.string.please_select_category_transaction)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilTransactionDate.error = null
                    binding.tilTransactionAmount.error = null
                    binding.tilTransactionMode.error = null
                    binding.tilTransactionCategory.error = null

                    // Update transaction
                    val token = sessionManager.getAuthToken()
                    if (token != null && shortUuid != null) {
                        viewModel.updateTransaction(
                            token = token,
                            shortUuid = shortUuid!!,
                            date = selectedDate!!,
                            title = title.ifBlank { null },
                            amount = amount.toInt(),
                            mode = selectedMode!!,
                            bankId = selectedBankId,
                            categoryId = selectedCategoryId!!,
                            tagIds = selectedTagIds.toList()
                        )
                    } else if (token == null) {
                        showError(getString(R.string.authentication_token_not_found))
                    } else {
                        showError("Transaction ID not found")
                    }
                }
            }
        }
    }

    /**
     * Shows the Jalali date picker dialog.
     */
    private fun showDatePicker() {
        val jalaliDatePickerDialog = JalaliDatePickerDialog(
            requireContext(),
            { year, month, day ->
                // Format the selected Jalali date
                selectedDate = JalaliDateUtils.formatJalaliDate(year, month, day)

                // Display the date with Persian numerals
                val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
                binding.etTransactionDate.setText(displayDate)
                binding.tilTransactionDate.error = null
            }
        )
        jalaliDatePickerDialog.show()
    }

    /**
     * Shows the tags selection dialog.
     */
    private fun showTagsDialog() {
        if (availableTags.isEmpty()) {
            Toast.makeText(requireContext(), getString(R.string.error_loading_tags), Toast.LENGTH_SHORT).show()
            return
        }

        val tagTitles = availableTags.map { it.title }.toTypedArray()
        val checkedItems = BooleanArray(tagTitles.size) { index ->
            availableTags[index].id in selectedTagIds
        }

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.transaction_tags))
            .setMultiChoiceItems(tagTitles, checkedItems) { _, which, isChecked ->
                val tag = availableTags[which]
                if (isChecked) {
                    if (tag.id !in selectedTagIds) {
                        selectedTagIds.add(tag.id)
                        selectedTagTitles.add(tag.title)
                    }
                } else {
                    selectedTagIds.remove(tag.id)
                    selectedTagTitles.remove(tag.title)
                }
            }
            .setPositiveButton(getString(R.string.submit)) { _, _ ->
                binding.etTransactionTags.setText(selectedTagTitles.joinToString(", "))
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }

    /**
     * Fetches initial data from the API.
     */
    private fun fetchData() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadModes(token)
            viewModel.loadBanks(token)
            viewModel.loadCategories(token)
            viewModel.loadTags(token)
        } else {
            showError(getString(R.string.authentication_token_not_found))
        }
    }

    /**
     * Sets up observers for ViewModel LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            if (error.isNotEmpty()) {
                showError(error)
            }
        }

        viewModel.modes.observe(viewLifecycleOwner) { modes ->
            setupModeDropdown(modes)
        }

        viewModel.banks.observe(viewLifecycleOwner) { bankResponse ->
            setupBankDropdown(bankResponse.results)
        }

        viewModel.categories.observe(viewLifecycleOwner) { categoryResponse ->
            setupCategoryDropdown(categoryResponse.results)
        }

        viewModel.tags.observe(viewLifecycleOwner) { tagResponse ->
            availableTags = tagResponse.results
        }

        viewModel.transactionUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is EditTransactionViewModel.TransactionUpdateResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.transaction_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate back to home
                    findNavController().navigate(R.id.nav_home)
                }
                is EditTransactionViewModel.TransactionUpdateResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Sets up the mode dropdown with available modes.
     */
    private fun setupModeDropdown(modes: List<TransactionMode>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            modes.map { it.label }
        )
        binding.dropdownTransactionMode.setAdapter(adapter)

        // Set current selection
        selectedMode?.let { currentMode ->
            val index = modes.indexOfFirst { it.value == currentMode }
            if (index >= 0) {
                binding.dropdownTransactionMode.setText(modes[index].label, false)
            }
        }
    }

    /**
     * Sets up the bank dropdown with available banks.
     */
    private fun setupBankDropdown(banks: List<BankItem>) {
        val bankTitles = mutableListOf<String>()
        bankTitles.add("") // Add empty option for optional field
        bankTitles.addAll(banks.map { it.title })

        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            bankTitles
        )
        binding.dropdownTransactionBank.setAdapter(adapter)

        // Set current selection
        selectedBankId?.let { currentBankId ->
            val index = banks.indexOfFirst { it.id == currentBankId }
            if (index >= 0) {
                binding.dropdownTransactionBank.setText(banks[index].title, false)
            }
        }
    }

    /**
     * Sets up the category dropdown with available categories.
     */
    private fun setupCategoryDropdown(categories: List<TransactionCategoryItem>) {
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            categories.map { it.title }
        )
        binding.dropdownTransactionCategory.setAdapter(adapter)

        // Set current selection
        selectedCategoryId?.let { currentCategoryId ->
            val index = categories.indexOfFirst { it.id == currentCategoryId }
            if (index >= 0) {
                binding.dropdownTransactionCategory.setText(categories[index].title, false)
            }
        }
    }

    /**
     * Shows an error message to the user.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE

        // Hide error after 5 seconds
        binding.tvError.postDelayed({
            binding.tvError.visibility = View.GONE
        }, 5000)
    }
}
