package ir.rahavardit.ariel.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.RecyclerView
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.model.EventObject

class EventObjectAdapter(
    private var eventList: List<EventObject>,
    private val onDeleteClick: (String) -> Unit,
    private val onEditClick: (String, EventObject) -> Unit
) : RecyclerView.Adapter<EventObjectAdapter.EventObjectViewHolder>() {

    class EventObjectViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EventObjectViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_event_object, parent, false)
        return EventObjectViewHolder(view)
    }

    override fun onBindViewHolder(holder: EventObjectViewHolder, position: Int) {
        val event = eventList[position]
        
        holder.textShortUuid.text = event.shortUuid
        holder.textTitle.text = event.title
        holder.textCreated.text = event.slashedDatePersian

        // Setup menu button click listener
        holder.btnMenu.setOnClickListener { view ->
            val popup = PopupMenu(view.context, view)
            popup.menuInflater.inflate(R.menu.item_menu, popup.menu)
            popup.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.action_edit -> {
                        onEditClick(event.shortUuid, event)
                        true
                    }
                    R.id.action_delete -> {
                        showDeleteConfirmationDialog(view, event.shortUuid)
                        true
                    }
                    else -> false
                }
            }
            popup.show()
        }
    }

    override fun getItemCount(): Int = eventList.size

    fun updateData(newEventList: List<EventObject>) {
        eventList = newEventList
        notifyDataSetChanged()
    }

    /**
     * Shows a confirmation dialog before deleting the event item.
     */
    private fun showDeleteConfirmationDialog(view: View, shortUuid: String) {
        val dialogView = LayoutInflater.from(view.context).inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = view.context.getString(R.string.delete_event_confirmation_title)
        messageTextView.text = view.context.getString(R.string.delete_event_confirmation_message)

        val dialog = AlertDialog.Builder(view.context)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            onDeleteClick(shortUuid)
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }
}
